{"// === 核心任务定义 ===": "这些字段定义了AI要做什么、为谁做、以什么身份做", "task": "清晰、可执行的核心任务指令。例如：'撰写一篇关于LoRA的技术教程'。必须是一个动词开头的明确动作。", "persona": "AI在执行任务时应扮演的角色或身份。例如：'资深技术布道者'、'心理咨询师'、'商业分析师'。角色决定语气和知识深度。", "context": "关于此任务的背景信息、前因后果或更大的目标。解释为什么要做这件事，比如：'帮助开发者理解高效微调方法，降低大模型使用门槛'。", "input_data": "[请用户提供详细信息] 用户提供的需要被处理的原始文本、数据或信息。如果是代码、文章草稿、用户反馈等，请粘贴在此处。若无提供，保留占位符。", "input_type": "text | code | data | url | file_reference | none // 输入数据的类型，帮助AI预处理。可选值：文本、代码、结构化数据、网页链接、文件引用、无输入。", "target_audience": "最终产出内容的目标受众是谁。例如：'刚入门机器学习的开发者'、'企业高管'、'高中生'。受众决定语言复杂度和举例方式。", "deliverables": ["一个或多个具体、明确的可交付成果列表。例如：'一篇1500字以内的技术文章'、'一个Python函数'、'一份PPT大纲'。必须是数组形式，即使只有一个成果。"], "structure_outline": "期望输出内容的具体结构、布局或章节安排。例如：'1. 引言 2. 原理 3. 示例 4. 总结'。用于确保输出逻辑清晰、结构统一。", "// === 风格与约束 ===": "控制输出的风格、内容边界和格式要求", "constraints_and_exclusions": {"must_include": ["必须包含的关键词或核心要点。例如：'LoRA'、'参数效率'。确保关键信息不遗漏。"], "must_not_include": ["绝对不能提及的主题、词语或信息。例如：'竞品名称'、'政治敏感话题'、'复杂数学推导'。用于内容过滤。"], "length": "对长度的要求，如'不超过1500字'、'三段以内'、'简明扼要'、'详细展开'。", "complexity_level": "入门级 | 中级 | 高级 | 专家级 // 控制内容的难易程度，匹配目标受众的知识水平。"}, "tone_and_style": "输出内容的语气、风格和写作水平，如'专业严谨'、'通俗易懂'、'幽默风趣'、'适合初学者'、'学术化'。影响遣词造句和表达方式。", "output_format": "最终交付成果的格式，如'Markdown'、'JSON'、'Python代码块'、'HTML'、'LaTeX'、'CSV'、'PowerPoint'。确保输出可直接使用。", "language": "输出内容所使用的语言，如'简体中文'、'English'、'日本語'。必须明确指定，避免AI自行切换语言。", "// === 领域与执行环境 ===": "说明任务所属领域及运行/使用环境", "knowledge_domain": ["任务所属的专业知识领域，例如：'人工智能'、'心理学'、'法律'、'金融分析'、'教育'。用于AI调用正确的知识库或路由到专家模型。"], "required_tools_or_libraries": ["执行任务所需的技术工具或编程库，例如：'pandas'、'matplotlib'、'scikit-learn'、'LaTeX'。若生成代码，需声明依赖项。"], "execution_environment": "输出内容将被使用的环境，如'Jupyter Notebook'、'生产服务器'、'微信公众号'、'PPT演示文稿'、'CLI命令行'。不同环境对格式要求不同。", "// === 引用与事实性要求 ===": "控制内容的可信度和来源规范", "citations_or_sources": {"required": false, "format": "引用格式，如APA、MLA、IEEE、自定义等", "allowed_sources": ["允许引用的信息来源，如：'学术论文'、'官方文档'、'维基百科'、'开源项目'。"], "prohibited_sources": ["禁止引用的信息来源，如：'知乎匿名回答'、'未经验证的博客'、'社交媒体帖子'。"]}, "// === 安全与合规性 ===": "确保内容安全、合规、无偏见", "safety_constraints": {"avoid_bias": true, "content_moderation_level": "内容审核严格程度：none | moderate | strict", "compliance_standards": ["需遵守的法律法规或行业标准，如：'GDPR'、'CCPA'、'中国网络信息内容生态治理规定'、'HIPAA'。"], "political_neutrality": true, "religious_sensitivity": true, "age_appropriateness": "general_audience | teen | adult_only // 内容适龄性"}, "ethical_guidelines": ["AI应遵守的伦理准则，如：'不编造事实'、'不诱导用户'、'尊重隐私'、'避免误导性表述'。"], "// === 系统集成与工作流 ===": "支持AI代理系统、任务链、反馈机制", "dependencies": [{"task_id": "前置依赖任务的唯一ID，如analyze-sales-data-v1", "output_key": "所需依赖的输出字段名", "required_before_start": true}], "feedback_loop_enabled": true, "max_iterations": 3, "confidence_threshold": 0.85, "// confidence_threshold说明": "AI只有在置信度高于该值时才输出结果，否则请求澄清。范围0.0~1.0，越高越保守。", "cost_optimization": {"objective": "minimize_tokens | maximize_speed | balance_quality_and_cost", "max_tokens": 2048, "prefer_streaming": false}, "monitoring_metrics": ["用于评估输出质量的指标，如：'accuracy'（准确性）、'readability_score'（可读性）、'factuality_rate'（事实性）、'user_satisfaction'（用户满意度）"], "// === 元信息 ===": "用于追踪、版本管理和系统集成", "version": "2.0", "// version说明": "提示词模板的版本号，便于迭代和兼容性管理。", "last_updated": "2025-04-05T10:30:00Z", "// last_updated说明": "ISO 8601格式的时间戳，表示此提示词最后一次更新时间。建议每次生成时更新。", "task_id": "唯一任务标识符，用于工作流追踪。可由系统生成，如'tutorial-lora-intro-v1'或'auto-generated-20250405'。"}