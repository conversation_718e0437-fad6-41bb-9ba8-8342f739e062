#!/usr/bin/env python3
"""
SoSoValue 监控器重启脚本
当检测到 SoSoValue 监控器停止时，自动重启它
"""

import time
import subprocess
import json
import os
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sosovalue_restart.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SoSoValueMonitor:
    """SoSoValue 监控器状态检查和重启"""
    
    def __init__(self):
        self.news_file = "financial_news.json"
        self.last_check_time = None
        self.restart_count = 0
        self.max_restarts_per_hour = 3
        self.restart_times = []
        
    def check_sosovalue_activity(self) -> bool:
        """检查 SoSoValue 是否有新的活动"""
        try:
            if not os.path.exists(self.news_file):
                logger.warning(f"新闻文件不存在: {self.news_file}")
                return False
                
            with open(self.news_file, 'r', encoding='utf-8') as f:
                news_data = json.load(f)
            
            # 查找最近的 SoSoValue 记录
            sosovalue_records = [
                record for record in news_data 
                if record.get('source') == 'SoSoValue研究'
            ]
            
            if not sosovalue_records:
                logger.warning("没有找到 SoSoValue 记录")
                return False
            
            # 获取最新记录的时间
            latest_record = sosovalue_records[-1]
            latest_time_str = latest_record.get('timestamp', '')
            
            try:
                latest_time = datetime.strptime(latest_time_str, '%Y-%m-%d %H:%M:%S')
                current_time = datetime.now()
                time_diff = current_time - latest_time
                
                # 如果超过5分钟没有新记录，认为可能停止了
                if time_diff > timedelta(minutes=5):
                    logger.warning(f"SoSoValue 最后活动时间: {latest_time_str}, 已经 {time_diff} 没有新记录")
                    return False
                else:
                    logger.info(f"SoSoValue 运行正常，最后活动: {latest_time_str}")
                    return True
                    
            except ValueError as e:
                logger.error(f"时间格式解析错误: {e}")
                return False
                
        except Exception as e:
            logger.error(f"检查 SoSoValue 活动时出错: {e}")
            return False
    
    def can_restart(self) -> bool:
        """检查是否可以重启（避免频繁重启）"""
        current_time = datetime.now()
        
        # 清理1小时前的重启记录
        self.restart_times = [
            restart_time for restart_time in self.restart_times
            if current_time - restart_time < timedelta(hours=1)
        ]
        
        # 检查1小时内重启次数
        if len(self.restart_times) >= self.max_restarts_per_hour:
            logger.warning(f"1小时内已重启 {len(self.restart_times)} 次，暂停重启")
            return False
        
        return True
    
    def restart_monitor(self) -> bool:
        """重启监控器"""
        try:
            if not self.can_restart():
                return False
            
            logger.info("🔄 正在重启 SoSoValue 监控器...")
            
            # 记录重启时间
            self.restart_times.append(datetime.now())
            self.restart_count += 1
            
            # 停止现有进程
            try:
                subprocess.run(['pkill', '-f', 'jin10_web_monitor.py'], 
                             capture_output=True, text=True, timeout=10)
                logger.info("已停止现有监控进程")
                time.sleep(3)
            except Exception as e:
                logger.warning(f"停止进程时出错: {e}")
            
            # 启动新进程
            process = subprocess.Popen(
                ['python', 'jin10_web_monitor.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            logger.info(f"✅ 监控器已重启 (第 {self.restart_count} 次)")
            logger.info(f"新进程 PID: {process.pid}")
            
            # 等待一段时间检查是否启动成功
            time.sleep(10)
            
            if process.poll() is None:
                logger.info("监控器启动成功")
                return True
            else:
                logger.error("监控器启动失败")
                return False
                
        except Exception as e:
            logger.error(f"重启监控器时出错: {e}")
            return False
    
    def run(self):
        """运行监控循环"""
        logger.info("🚀 SoSoValue 监控器守护进程已启动")
        logger.info("📋 监控规则:")
        logger.info("  - 检查间隔: 60秒")
        logger.info("  - 无活动超时: 5分钟")
        logger.info("  - 最大重启频率: 3次/小时")
        logger.info("🛑 按 Ctrl+C 停止")
        
        try:
            while True:
                # 检查 SoSoValue 活动状态
                is_active = self.check_sosovalue_activity()
                
                if not is_active:
                    logger.warning("⚠️ 检测到 SoSoValue 监控器可能已停止")
                    
                    # 尝试重启
                    restart_success = self.restart_monitor()
                    
                    if restart_success:
                        logger.info("✅ 重启成功，继续监控")
                    else:
                        logger.error("❌ 重启失败，等待下次检查")
                
                # 等待下次检查
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("🛑 收到停止信号，退出守护进程")
        except Exception as e:
            logger.error(f"守护进程运行时出错: {e}")

def main():
    """主函数"""
    monitor = SoSoValueMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
