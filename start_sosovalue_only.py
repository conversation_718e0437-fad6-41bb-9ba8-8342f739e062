#!/usr/bin/env python3
"""
只启动 SoSoValue 监控器的脚本
用于测试和调试 SoSoValue 网站的抓取
"""

import sys
import os
import time
from config import Config

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 启动 SoSoValue 专用监控器...")
    
    # 加载配置
    config = Config()
    
    # 查找 SoSoValue 配置
    sosovalue_config = None
    for website in config.websites:
        if website.name == "SoSoValue研究":
            sosovalue_config = website
            break
    
    if not sosovalue_config:
        print("❌ 未找到 SoSoValue 配置")
        return
    
    if not sosovalue_config.enabled:
        print("❌ SoSoValue 监控器未启用，请检查配置")
        return
    
    print(f"📋 配置信息:")
    print(f"  - 网站: {sosovalue_config.name}")
    print(f"  - URL: {sosovalue_config.url}")
    print(f"  - 检查间隔: {sosovalue_config.check_interval}秒")
    print(f"  - 选择器数量: {len(sosovalue_config.selectors)}")
    
    # 导入监控器类
    try:
        from jin10_web_monitor import WebsiteMonitor, NewsJSONStorage
    except ImportError as e:
        print(f"❌ 导入监控器失败: {e}")
        return
    
    # 创建JSON存储
    json_storage = NewsJSONStorage("financial_news.json")
    
    # 创建监控器
    monitor = WebsiteMonitor(sosovalue_config, config, json_storage)
    
    print("🔄 正在启动监控器...")
    
    try:
        # 运行监控器
        monitor.run()
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号...")
    except Exception as e:
        print(f"❌ 监控器运行失败: {e}")
    finally:
        monitor.stop()
        print("✅ 监控器已停止")

if __name__ == "__main__":
    main()
